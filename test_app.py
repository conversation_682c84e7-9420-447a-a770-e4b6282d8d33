#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单测试脚本，验证程序的基本功能
"""

from windsurf_switcher import WindsurfSwitcher
import time

def test_windsurf_switcher():
    print("=== 测试Windsurf切换器 ===\n")
    
    # 创建切换器实例
    switcher = WindsurfSwitcher()
    
    # 1. 诊断配置
    print("1. 诊断Windsurf配置:")
    existing_dirs = switcher.diagnose_config()
    
    # 2. 检查当前Token
    print("\n2. 检查当前Token:")
    current_token = switcher.get_current_token()
    if current_token:
        print(f"找到当前Token: {current_token[:20]}...")
    else:
        print("未找到当前Token")
    
    # 3. 检查Windsurf进程
    print(f"\n3. Windsurf进程状态: {'运行中' if switcher.is_windsurf_running() else '未运行'}")
    
    # 4. 测试Token验证
    print("\n4. 测试Token验证:")
    test_tokens = [
        "invalid",  # 无效token
        "this-is-a-test-token-12345",  # 有效格式
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test"  # JWT格式
    ]
    
    for token in test_tokens:
        is_valid, message = switcher.validate_token(token)
        print(f"  Token: {token[:20]}... -> {'✓' if is_valid else '✗'} {message}")
    
    print("\n=== 测试完成 ===")
    return existing_dirs

if __name__ == "__main__":
    try:
        test_windsurf_switcher()
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()