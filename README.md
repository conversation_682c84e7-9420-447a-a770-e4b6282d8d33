# Windsurf账号管理器

一个用于批量存储和无感切换Windsurf账号token的桌面应用程序。

## 功能特点

### 核心功能
- 🔐 **安全加密存储**：使用AES加密算法保护敏感的token信息
- 🔄 **无感自动切换**：支持手动和自动切换账号，提高工作效率
- 📊 **使用监控统计**：实时监控账号使用情况和统计信息
- 💾 **本地文件存储**：无需数据库，数据存储在本地加密文件中
- 📁 **批量导入导出**：支持批量管理账号信息

### 界面功能
- **账号管理**：添加、更新、删除账号，测试token有效性
- **切换控制**：快速切换账号，自动切换设置
- **监控统计**：查看使用统计，实时监控日志

## 安装说明

### 系统要求
- Windows 10/11
- Python 3.8+

### 安装步骤

1. 克隆或下载项目文件到本地
2. 安装依赖包：
```bash
pip install -r requirements.txt
```

3. 运行应用程序：
```bash
python main.py
```

## 使用指南

### 首次使用

1. **设置管理密码**
   - 首次启动时，输入一个管理密码
   - 此密码用于加密存储账号数据，请妥善保管
   - 点击"解锁"按钮开始使用

2. **添加账号**
   - 在"账号管理"标签页中
   - 填写账号名称和Windsurf Token
   - 点击"添加账号"按钮

### 账号管理

#### 添加账号
1. 在账号管理页面填写：
   - **账号名称**：便于识别的名称
   - **Token**：Windsurf账号的API Token
2. 点击"测试Token"验证有效性（可选）
3. 点击"添加账号"保存

#### 更新账号
1. 从账号列表中选择要更新的账号
2. 修改账号名称或Token
3. 点击"更新账号"保存更改

#### 删除账号
1. 从账号列表中选择要删除的账号
2. 点击"删除账号"
3. 确认删除操作

### 账号切换

#### 手动切换
1. 在"切换控制"标签页中
2. 从列表中选择要切换的账号
3. 双击账号或点击"立即切换"按钮

#### 自动切换设置
1. 勾选"启用自动切换"
2. 设置切换间隔（分钟）
3. 选择切换策略：
   - **轮询**：按顺序轮流使用
   - **随机**：随机选择账号
   - **负载均衡**：根据使用情况自动选择

### 监控统计

#### 实时监控
1. 在"监控统计"标签页中
2. 勾选"启用实时监控"
3. 查看实时信息窗口的监控日志

#### 使用统计
- 查看每个账号的使用次数
- 最后使用时间
- 总使用时长统计

### 批量操作

#### 导入账号
1. 准备JSON格式的账号文件
2. 点击"导入账号"按钮
3. 选择要导入的文件

**JSON格式示例：**
```json
[
  {
    "name": "账号1",
    "token": "your_token_here",
    "usage_count": 0,
    "total_time": 0
  },
  {
    "name": "账号2", 
    "token": "your_token_here2",
    "usage_count": 0,
    "total_time": 0
  }
]
```

#### 导出账号
1. 点击"导出账号"按钮
2. 选择保存位置
3. 账号信息将以JSON格式导出

## 数据存储

### 存储位置
程序数据存储在用户主目录下的 `.windsurf_manager` 文件夹中：
- `accounts.enc`：加密的账号数据
- `config.json`：配置信息

### 安全性
- 所有敏感数据使用AES-256加密
- 基于用户密码生成加密密钥
- 本地存储，不会上传到任何服务器

## 常见问题

### Q: 忘记管理密码怎么办？
A: 需要删除 `~/.windsurf_manager` 文件夹重新开始。注意这将丢失所有已保存的账号数据。

### Q: 如何备份账号数据？
A: 使用"导出账号"功能将数据导出为JSON文件进行备份。

### Q: Token验证失败怎么办？
A: 请检查Token是否正确，或者检查网络连接。程序目前提供模拟验证功能。

### Q: 程序支持哪些操作系统？
A: 目前主要支持Windows系统，理论上也可以在macOS和Linux上运行。

## 开发说明

### 技术栈
- **GUI框架**：Tkinter
- **加密库**：cryptography
- **数据格式**：JSON
- **编程语言**：Python 3.8+

### 项目结构
```
├── main.py              # 主程序文件
├── requirements.txt     # 依赖包列表
└── README.md           # 说明文档
```

### 扩展功能
未来可以考虑添加的功能：
- 支持更多AI工具的账号管理
- 云端同步功能
- 更丰富的监控指标
- 插件系统

## 许可证

本项目仅供个人学习和使用，请遵守相关服务的使用条款。

## 贡献

欢迎提交Bug报告和功能建议。

---

**注意：请确保遵守Windsurf等相关服务的使用条款和政策。**