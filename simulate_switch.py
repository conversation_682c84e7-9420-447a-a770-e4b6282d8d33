#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模拟真实的账号切换测试
这个脚本会创建实际的配置文件，演示完整的切换流程
"""

import json
import os
from pathlib import Path
from windsurf_switcher import WindsurfSwitcher
from datetime import datetime

def create_test_tokens():
    """创建测试用的Token数据"""
    return [
        {
            "name": "工作账号",
            "token": "sk-work-abc123def456ghi789jkl012mno345pqr678stu901vwx234yz",
            "usage_count": 0,
            "total_time": 0
        },
        {
            "name": "个人账号", 
            "token": "sk-personal-def456ghi789jkl012mno345pqr678stu901vwx234yz567ab",
            "usage_count": 0,
            "total_time": 0
        },
        {
            "name": "开发账号",
            "token": "sk-dev-ghi789jkl012mno345pqr678stu901vwx234yz567abc890def123",
            "usage_count": 0,
            "total_time": 0
        }
    ]

def simulate_switch_test():
    """模拟完整的切换测试"""
    print("=== 🚀 Windsurf账号切换模拟测试 ===\n")
    
    # 1. 初始化切换器
    switcher = WindsurfSwitcher()
    
    # 2. 显示当前状态
    print("📋 当前状态检查:")
    current_token = switcher.get_current_token()
    if current_token:
        print(f"  当前Token: {current_token[:30]}...")
    else:
        print("  当前Token: 未设置")
    print(f"  Windsurf进程: {'✅ 运行中' if switcher.is_windsurf_running() else '❌ 未运行'}")
    print()
    
    # 3. 获取测试数据
    test_accounts = create_test_tokens()
    print("🎯 准备测试账号:")
    for i, account in enumerate(test_accounts, 1):
        print(f"  {i}. {account['name']}: {account['token'][:30]}...")
    print()
    
    # 4. 模拟切换流程
    print("🔄 开始模拟切换测试:")
    
    for i, account in enumerate(test_accounts):
        print(f"\n--- 测试 {i+1}: 切换到 '{account['name']}' ---")
        
        # Token验证
        is_valid, message = switcher.validate_token(account['token'])
        print(f"  Token验证: {'✅' if is_valid else '❌'} {message}")
        
        if is_valid:
            # 执行切换
            print(f"  执行切换...")
            success = switcher.switch_token(account['token'], account['name'])
            
            if success:
                print(f"  ✅ 成功切换到 '{account['name']}'")
                
                # 验证切换结果
                new_token = switcher.get_current_token()
                if new_token:
                    if account['token'] in new_token or new_token in account['token']:
                        print(f"  ✅ 切换验证成功")
                    else:
                        print(f"  ⚠️  切换验证: Token不匹配")
                        print(f"     期望: {account['token'][:30]}...")
                        print(f"     实际: {new_token[:30]}...")
                else:
                    print(f"  ⚠️  无法读取切换后的Token")
            else:
                print(f"  ❌ 切换失败")
        
        # 暂停一下，模拟真实使用
        import time
        time.sleep(1)
    
    print(f"\n--- 🏁 测试完成 ---")
    
    # 5. 显示最终状态
    final_token = switcher.get_current_token()
    print(f"\n📊 最终状态:")
    print(f"  当前Token: {final_token[:30] + '...' if final_token else '未设置'}")
    
    # 6. 检查创建的配置文件
    print(f"\n📁 创建的配置文件:")
    config_files = [
        switcher.config_paths["auth"],
        switcher.config_paths["settings"],
        switcher.config_paths["base"] / "current_auth.json"
    ]
    
    for config_file in config_files:
        if config_file.exists():
            size = config_file.stat().st_size
            print(f"  ✅ {config_file} ({size} 字节)")
        else:
            print(f"  ❌ {config_file} (不存在)")
    
    print(f"\n=== 🎉 模拟测试完成 ===")
    return final_token

def cleanup_test_files():
    """清理测试文件"""
    switcher = WindsurfSwitcher()
    test_files = [
        switcher.config_paths["auth"],
        switcher.config_paths["settings"], 
        switcher.config_paths["base"] / "current_auth.json"
    ]
    
    print("\n🧹 清理测试文件:")
    for file_path in test_files:
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"  ✅ 删除: {file_path}")
            except Exception as e:
                print(f"  ❌ 删除失败: {file_path} - {e}")

if __name__ == "__main__":
    try:
        # 运行模拟测试
        final_token = simulate_switch_test()
        
        # 询问是否清理
        print(f"\n❓ 是否清理测试文件？")
        print(f"输入 'clean' 清理测试文件，或按回车保留:")
        user_input = input().strip().lower()
        
        if user_input == 'clean':
            cleanup_test_files()
            print("✅ 清理完成")
        else:
            print("📁 测试文件已保留，可用于实际测试")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
