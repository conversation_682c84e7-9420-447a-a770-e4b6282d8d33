# Windsurf账号管理器 - 版本信息

## 当前版本：v1.0.0

### 发布日期：2025-09-04

### 功能特性

#### 核心功能 ✅
- [x] 安全加密存储账号Token
- [x] 手动切换Windsurf账号
- [x] 自动定时切换账号
- [x] 实时系统监控
- [x] 使用统计分析
- [x] 批量导入导出账号

#### 界面功能 ✅
- [x] 直观的图形用户界面
- [x] 三个主要功能标签页
- [x] 实时日志显示
- [x] 状态栏信息提示
- [x] 密码保护机制

#### 安全特性 ✅
- [x] AES-256加密算法
- [x] 基于密码的密钥生成
- [x] 本地文件存储
- [x] 配置文件备份
- [x] Token有效性验证

#### 切换策略 ✅
- [x] 轮询切换
- [x] 随机切换  
- [x] 负载均衡切换
- [x] 自定义时间间隔
- [x] 进程状态检测

### 技术规格

#### 开发环境
- **编程语言**：Python 3.8+
- **GUI框架**：Tkinter
- **加密库**：cryptography 41.0.7
- **系统监控**：psutil 5.9.6
- **网络请求**：requests 2.31.0

#### 支持平台
- ✅ Windows 10/11
- ⚠️ macOS (理论支持，未测试)
- ⚠️ Linux (理论支持，未测试)

#### 系统要求
- **最低内存**：512MB RAM
- **存储空间**：50MB
- **网络**：用于Token验证
- **权限**：读写用户目录权限

### 文件结构
```
├── main.py                 # 主程序文件 (40KB)
├── windsurf_switcher.py    # 切换核心模块 (6KB)
├── requirements.txt        # Python依赖包
├── start.bat              # Windows启动脚本
├── README.md              # 项目说明文档
├── 安装使用说明.md         # 详细使用指南
├── 账号导入示例.json       # 导入格式示例
└── VERSION.md             # 版本信息文件
```

### 已知限制

#### 功能限制
- Token验证目前为模拟实现
- 需要手动获取Windsurf配置路径
- 自动重启功能需要用户确认

#### 平台限制  
- 主要针对Windows平台优化
- macOS和Linux支持待完善
- Windsurf配置路径可能因版本而异

### 更新计划

#### v1.1.0 (计划中)
- [ ] 真实的Windsurf API集成
- [ ] 更智能的配置文件检测
- [ ] 云端同步功能
- [ ] 多语言界面支持

#### v1.2.0 (计划中)
- [ ] 插件系统
- [ ] 自定义主题
- [ ] 高级监控指标
- [ ] 账号分组管理

#### v2.0.0 (远期计划)
- [ ] Web界面版本
- [ ] 团队协作功能
- [ ] 高级安全特性
- [ ] 多工具支持

### 贡献指南

欢迎提交：
- 🐛 Bug报告
- 💡 功能建议  
- 🔧 代码改进
- 📚 文档完善

### 许可证

本项目采用MIT许可证，仅供学习和个人使用。

### 免责声明

- 本工具仅供合法使用
- 请遵守Windsurf服务条款
- 开发者不承担使用风险
- 数据安全由用户自行负责

---

**开发者**：AI Assistant  
**最后更新**：2025-09-04  
**状态**：稳定版本