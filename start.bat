@echo off
chcp 65001 > nul
echo 正在启动Windsurf账号管理器...
echo.

REM 检查Python是否安装
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖包...
pip show cryptography > nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo 依赖包安装失败，请手动执行：pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo 启动应用程序...
python main.py

if %errorlevel% neq 0 (
    echo.
    echo 程序运行出错，请检查错误信息
    pause
)