"""
Windsurf账号切换核心模块
实现实际的Token切换逻辑
"""

import os
import json
import subprocess
import platform
import time
from datetime import datetime
from pathlib import Path

class WindsurfSwitcher:
    def __init__(self):
        self.system = platform.system()
        self.config_paths = self._get_config_paths()
    
    def _get_config_paths(self):
        """获取Windsurf配置文件路径"""
        if self.system == "Windows":
            # Windows路径 - 多个可能的位置
            possible_bases = [
                Path.home() / "AppData" / "Roaming" / "windsurf-editor",
                Path.home() / "AppData" / "Roaming" / "Windsurf", 
                Path.home() / "AppData" / "Local" / "windsurf-editor",
                Path.home() / "AppData" / "Local" / "Windsurf",
                Path.home() / ".windsurf",
                Path.home() / ".config" / "windsurf"
            ]
            
            # 尝试找到存在的配置目录
            for base_path in possible_bases:
                if base_path.exists():
                    return {
                        "settings": base_path / "User" / "settings.json",
                        "auth": base_path / "User" / "globalStorage" / "auth.json",
                        "user_data": base_path / "User",
                        "base": base_path
                    }
            
            # 如果都不存在，使用默认路径
            default_base = Path.home() / "AppData" / "Roaming" / "Windsurf"
            return {
                "settings": default_base / "User" / "settings.json",
                "auth": default_base / "User" / "globalStorage" / "auth.json", 
                "user_data": default_base / "User",
                "base": default_base
            }
        elif self.system == "Darwin":  # macOS
            base_path = Path.home() / "Library" / "Application Support" / "Windsurf"
            return {
                "settings": base_path / "User" / "settings.json", 
                "auth": base_path / "User" / "globalStorage" / "auth.json",
                "user_data": base_path / "User",
                "base": base_path
            }
        else:  # Linux
            base_path = Path.home() / ".config" / "Windsurf"
            return {
                "settings": base_path / "User" / "settings.json",
                "auth": base_path / "User" / "globalStorage" / "auth.json",
                "user_data": base_path / "User", 
                "base": base_path
            }
    
    def get_current_token(self):
        """获取当前激活的Token"""
        try:
            # 尝试多个可能的配置文件位置
            possible_files = [
                self.config_paths["auth"],
                self.config_paths["settings"],
                self.config_paths["user_data"] / "workspaceStorage" / "auth.json",
                self.config_paths["base"] / "auth.json",
                self.config_paths["base"] / "config.json"
            ]
            
            for auth_file in possible_files:
                if auth_file.exists():
                    try:
                        with open(auth_file, 'r', encoding='utf-8') as f:
                            auth_data = json.load(f)
                            
                            # 尝试多个可能的token字段
                            token_fields = ['token', 'access_token', 'auth_token', 'api_token', 'bearer_token']
                            for field in token_fields:
                                if field in auth_data and auth_data[field]:
                                    print(f"在 {auth_file} 中找到token: {field}")
                                    return auth_data[field]
                            
                            # 检查嵌套的对象
                            if 'user' in auth_data and isinstance(auth_data['user'], dict):
                                for field in token_fields:
                                    if field in auth_data['user'] and auth_data['user'][field]:
                                        return auth_data['user'][field]
                    except (json.JSONDecodeError, Exception) as e:
                        print(f"读取 {auth_file} 失败: {e}")
                        continue
            
            print("未在任何配置文件中找到token")
            return None
            
        except Exception as e:
            print(f"获取当前Token失败: {e}")
            return None
    
    def switch_token(self, new_token, account_name):
        """切换到新的Token"""
        try:
            print(f"开始切换到账号: {account_name}")
            
            # 备份当前配置
            self._backup_config()
            
            # 确保目录存在
            self.config_paths["user_data"].mkdir(parents=True, exist_ok=True)
            (self.config_paths["user_data"] / "globalStorage").mkdir(parents=True, exist_ok=True)
            
            # 更新认证信息 - 尝试多个文件
            success = False
            
            # 1. 更新主要的auth文件
            auth_file = self.config_paths["auth"]
            try:
                auth_data = {}
                if auth_file.exists():
                    with open(auth_file, 'r', encoding='utf-8') as f:
                        auth_data = json.load(f)
                
                # 更新token信息
                auth_data.update({
                    'token': new_token,
                    'access_token': new_token,
                    'api_token': new_token,
                    'account_name': account_name,
                    'last_switch': str(int(time.time())),
                    'switch_time': datetime.now().isoformat()
                })
                
                with open(auth_file, 'w', encoding='utf-8') as f:
                    json.dump(auth_data, f, indent=2, ensure_ascii=False)
                
                print(f"已更新 {auth_file}")
                success = True
            except Exception as e:
                print(f"更新 {auth_file} 失败: {e}")
            
            # 2. 更新settings文件
            settings_file = self.config_paths["settings"]
            try:
                settings_data = {}
                if settings_file.exists():
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings_data = json.load(f)
                
                # 在settings中也保存token信息
                settings_data.update({
                    'windsurf.auth.token': new_token,
                    'windsurf.account.current': account_name,
                    'windsurf.lastSwitchTime': datetime.now().isoformat()
                })
                
                settings_file.parent.mkdir(parents=True, exist_ok=True)
                with open(settings_file, 'w', encoding='utf-8') as f:
                    json.dump(settings_data, f, indent=2, ensure_ascii=False)
                
                print(f"已更新 {settings_file}")
                success = True
            except Exception as e:
                print(f"更新 {settings_file} 失败: {e}")
            
            # 3. 创建备用配置文件
            backup_auth = self.config_paths["base"] / "current_auth.json"
            try:
                backup_data = {
                    'current_token': new_token,
                    'current_account': account_name,
                    'switch_time': datetime.now().isoformat(),
                    'manager_info': 'Windsurf Account Manager'
                }
                
                with open(backup_auth, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, indent=2, ensure_ascii=False)
                
                print(f"已创建备用配置: {backup_auth}")
                success = True
            except Exception as e:
                print(f"创建备用配置失败: {e}")
            
            if success:
                # 重启Windsurf进程（如果正在运行）
                self._restart_windsurf()
                print(f"成功切换到账号: {account_name}")
                return True
            else:
                print("切换失败：未能更新任何配置文件")
                return False
            
        except Exception as e:
            print(f"切换Token失败: {e}")
            return False
    
    def _backup_config(self):
        """备份当前配置"""
        try:
            backup_dir = Path.home() / ".windsurf_manager" / "backup"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            for name, path in self.config_paths.items():
                if path.exists():
                    backup_file = backup_dir / f"{name}_backup.json"
                    subprocess.run(["copy" if self.system == "Windows" else "cp", 
                                  str(path), str(backup_file)], 
                                  capture_output=True)
        except Exception as e:
            print(f"备份配置失败: {e}")
    
    def _restart_windsurf(self):
        """重启Windsurf进程"""
        try:
            # 查找并关闭Windsurf进程
            if self.system == "Windows":
                subprocess.run(["taskkill", "/F", "/IM", "windsurf.exe"], 
                             capture_output=True)
                # 可以选择自动重启
                # subprocess.run(["start", "windsurf"], shell=True)
            else:
                subprocess.run(["pkill", "-f", "windsurf"], capture_output=True)
                # subprocess.run(["windsurf"], capture_output=True)
                
        except Exception as e:
            print(f"重启Windsurf失败: {e}")
    
    def is_windsurf_running(self):
        """检查Windsurf是否正在运行"""
        try:
            if self.system == "Windows":
                result = subprocess.run(["tasklist", "/FI", "IMAGENAME eq windsurf.exe"], 
                                      capture_output=True, text=True)
                return "windsurf.exe" in result.stdout
            else:
                result = subprocess.run(["pgrep", "-f", "windsurf"], 
                                      capture_output=True, text=True)
                return bool(result.stdout.strip())
        except Exception:
            return False
    
    def validate_token(self, token):
        """验证Token有效性"""
        try:
            # 基本格式验证
            if not token or len(token.strip()) < 10:
                return False, "Token长度不足（少于10个字符）"
            
            token = token.strip()
            
            # 检查Token格式
            if not token.replace('-', '').replace('_', '').isalnum():
                return False, "Token包含非法字符"
            
            # 尝试实际的API验证（如果有API端点）
            try:
                # 这里可以添加实际的Windsurf API调用
                # 目前使用本地验证
                
                # 检查Token是否已经存在于配置中
                current_token = self.get_current_token()
                if current_token == token:
                    return True, "Token已在使用中，验证成功"
                
                # 模拟API调用验证
                import time
                time.sleep(0.5)  # 模拟网络延迟
                
                # 基本的Token格式检查
                if len(token) >= 20 and len(token) <= 500:
                    return True, "Token格式验证通过"
                else:
                    return False, "Token长度不在合理范围内（20-500字符）"
                
            except Exception as api_error:
                # API验证失败，使用本地验证
                if len(token) >= 10:
                    return True, f"API验证失败，但Token格式正确: {str(api_error)}"
                else:
                    return False, f"Token格式错误: {str(api_error)}"
            
        except Exception as e:
            return False, f"验证失败: {str(e)}"
    
    def diagnose_config(self):
        """诊断配置文件状态"""
        print("\n=== Windsurf配置诊断 ===\n")
        
        # 1. 检查可能的配置目录
        possible_dirs = [
            Path.home() / "AppData" / "Roaming" / "Windsurf",
            Path.home() / "AppData" / "Roaming" / "windsurf-editor", 
            Path.home() / "AppData" / "Local" / "Windsurf",
            Path.home() / "AppData" / "Local" / "windsurf-editor",
            Path.home() / ".windsurf",
            Path.home() / ".config" / "windsurf"
        ]
        
        print("检查可能的Windsurf配置目录:")
        existing_dirs = []
        for dir_path in possible_dirs:
            exists = dir_path.exists()
            print(f"  {'✓' if exists else '✗'} {dir_path}")
            if exists:
                existing_dirs.append(dir_path)
                
                # 检查子目录
                user_dir = dir_path / "User"
                if user_dir.exists():
                    print(f"    └── User/ 目录存在")
                    
                    # 检查配置文件
                    config_files = [
                        user_dir / "settings.json",
                        user_dir / "globalStorage" / "auth.json",
                        user_dir / "workspaceStorage"
                    ]
                    
                    for config_file in config_files:
                        if config_file.exists():
                            if config_file.is_file():
                                size = config_file.stat().st_size
                                print(f"        ├── {config_file.name} ({size} bytes)")
                            else:
                                print(f"        ├── {config_file.name}/ (目录)")
        
        # 2. 检查当前配置
        print(f"\n当前使用的配置路径: {self.config_paths['base']}")
        print(f"配置目录存在: {'✓' if self.config_paths['base'].exists() else '✗'}")
        
        # 3. 尝试读取当前token
        print(f"\n尝试获取当前token:")
        current_token = self.get_current_token()
        if current_token:
            print(f"找到token: {current_token[:20]}...（已截断）")
        else:
            print("未找到token")
        
        # 4. 检查Windsurf进程
        print(f"\nWindsurf进程状态: {'✓ 运行中' if self.is_windsurf_running() else '✗ 未运行'}")
        
        # 5. 推荐配置
        if not existing_dirs:
            print("\n⚠️  未找到Windsurf配置目录")
            print("请确保:")
            print("  1. Windsurf已正确安装")
            print("  2. 至少运行过一次Windsurf")
            print("  3. 已登录你的账号")
        else:
            print(f"\n✓ 找到 {len(existing_dirs)} 个可能的配置目录")
        
        print("\n=== 诊断完成 ===\n")
        return existing_dirs