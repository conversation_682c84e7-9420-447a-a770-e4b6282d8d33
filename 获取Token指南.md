# 如何获取Windsurf Token

## 方法一：从配置文件获取（推荐）

### 1. 启动Windsurf并登录
1. 打开Windsurf应用
2. 确保已登录您的账号
3. 关闭Windsurf

### 2. 查找配置文件
根据测试结果，您的系统中有以下配置目录：
- ✅ `C:\Users\<USER>\AppData\Roaming\Windsurf` (主要)
- ✅ `C:\Users\<USER>\AppData\Local\Windsurf` (备用)

### 3. 查找Token文件
在以下位置查找Token：
```
C:\Users\<USER>\AppData\Roaming\Windsurf\User\settings.json
C:\Users\<USER>\AppData\Roaming\Windsurf\User\globalStorage\**\*.json
C:\Users\<USER>\AppData\Local\Windsurf\User\settings.json
```

### 4. 提取Token
打开JSON文件，查找包含以下字段的值：
- `token`
- `access_token` 
- `auth_token`
- `api_token`
- `bearer_token`

## 方法二：从浏览器开发者工具获取

### 1. 打开Windsurf Web版本
访问 Windsurf 网页版并登录

### 2. 打开开发者工具
- 按 `F12` 或右键 → 检查
- 转到 `Network` 标签

### 3. 刷新页面并查找请求
- 刷新页面
- 查找API请求
- 在请求头中找到 `Authorization: Bearer [token]`

## 方法三：手动获取（如果支持）

### 1. 账号设置
- 登录Windsurf账号
- 进入设置/偏好设置
- 查找 "API密钥" 或 "访问令牌" 选项

### 2. 生成新Token
- 创建新的API密钥
- 复制生成的Token

## 测试Token有效性

获取Token后，使用程序的"测试Token"功能验证：
1. 打开账号管理器
2. 输入Token
3. 点击"测试Token"按钮

## 常见Token格式

- **JWT格式**: `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...`
- **简单格式**: `sk-1234567890abcdef...`
- **UUID格式**: `550e8400-e29b-41d4-a716-************`

## 安全提醒

⚠️ **重要提醒**：
- Token是敏感信息，请妥善保管
- 不要在公共场所或截图中暴露Token
- 定期更换Token以确保安全
- 如Token泄露，立即生成新Token