# Windsurf账号管理器 - 安装使用说明

## 🚀 快速开始

### 第一步：安装Python
1. 访问 [Python官网](https://www.python.org/downloads/) 下载Python 3.8或更高版本
2. 安装时**务必勾选"Add Python to PATH"**选项
3. 安装完成后重启电脑

### 第二步：安装依赖包
1. 打开命令提示符或PowerShell
2. 切换到项目目录：
   ```
   cd "d:\AIGC-dm\无感切换"
   ```
3. 安装依赖包：
   ```
   pip install -r requirements.txt
   ```

### 第三步：运行程序
#### 方法1：双击启动脚本
- 双击 `start.bat` 文件

#### 方法2：命令行启动
```
python main.py
```

## 📋 依赖包说明

程序需要以下Python包（已在requirements.txt中定义）：
- `cryptography==41.0.7` - 用于数据加密
- `requests==2.31.0` - 用于网络请求
- `psutil==5.9.6` - 用于系统监控
- `pillow==10.1.0` - 用于图像处理（如需要）

## 🔧 首次使用

### 1. 设置管理密码
- 启动程序后，首先需要设置一个管理密码
- 此密码用于加密保护您的账号数据
- **请务必记住此密码，忘记后无法恢复数据**

### 2. 添加Windsurf账号
1. 点击"账号管理"标签页
2. 填写账号名称（自定义，便于识别）
3. 填写Windsurf Token
4. 点击"添加账号"

### 3. 获取Windsurf Token
要获取Windsurf Token，请按以下步骤操作：
1. 打开Windsurf应用
2. 进入设置/偏好设置
3. 找到账号或认证相关选项
4. 复制您的API Token或访问令牌

## 🔄 账号切换

### 手动切换
1. 在"切换控制"标签页中选择账号
2. 双击账号或点击"立即切换"
3. 如果Windsurf正在运行，程序会询问是否重启

### 自动切换
1. 勾选"启用自动切换"
2. 设置切换间隔（建议30分钟以上）
3. 选择切换策略：
   - **轮询**：按添加顺序依次切换
   - **随机**：随机选择账号
   - **负载均衡**：优先使用次数少的账号

## 📊 监控功能

### 实时监控
- 勾选"启用实时监控"查看系统状态
- 监控内容包括：
  - Windsurf运行状态
  - 当前活动账号
  - 系统资源使用情况

### 使用统计
查看每个账号的：
- 使用次数
- 最后使用时间
- 总使用时长

## 💾 数据管理

### 数据存储位置
- Windows: `C:\Users\<USER>\.windsurf_manager\`
- 包含加密的账号数据和配置文件

### 备份数据
1. 使用"导出账号"功能备份数据
2. 定期保存导出的JSON文件
3. 可在其他设备上使用"导入账号"恢复

### 导入/导出格式
JSON格式示例：
```json
[
  {
    "name": "工作账号",
    "token": "your_windsurf_token_here",
    "usage_count": 5,
    "total_time": 3600
  }
]
```

## ⚠️ 注意事项

### 安全提醒
- 管理密码一旦忘记无法恢复，请妥善保管
- Token信息敏感，请勿泄露给他人
- 定期备份账号数据

### 使用限制
- 请遵守Windsurf的使用条款
- 避免频繁切换账号（建议间隔30分钟以上）
- 确保Token的有效性和合法性

### 故障排除
1. **程序无法启动**
   - 检查Python是否正确安装
   - 确认依赖包已安装
   - 查看错误日志

2. **切换失败**
   - 检查Token是否有效
   - 确认Windsurf配置文件权限
   - 查看程序日志信息

3. **忘记管理密码**
   - 删除 `~/.windsurf_manager` 文件夹
   - 重新启动程序设置密码
   - 注意：这将丢失所有数据

## 🆘 获取帮助

如遇到问题：
1. 查看程序内的实时日志
2. 检查系统事件日志
3. 确认所有依赖正确安装
4. 验证Windsurf配置文件完整性

## 📝 更新日志

### v1.0.0
- 基础账号管理功能
- 加密存储实现
- 手动/自动切换功能
- 实时监控和统计
- 批量导入导出

---

**免责声明：本工具仅供个人学习和合法使用，请遵守相关服务条款。**