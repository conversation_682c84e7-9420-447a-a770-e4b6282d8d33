import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import base64
from cryptography.fernet import Fe<PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import hashlib
import threading
import time
from datetime import datetime
import requests
import psutil
import subprocess
import sys
from windsurf_switcher import WindsurfSwitcher

class WindsurfAccountManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Windsurf账号管理器 - 批量存储与无感切换")
        self.root.geometry("900x600")
        self.root.resizable(True, True)
        
        # 数据文件路径
        self.data_dir = os.path.join(os.path.expanduser("~"), ".windsurf_manager")
        self.accounts_file = os.path.join(self.data_dir, "accounts.enc")
        self.config_file = os.path.join(self.data_dir, "config.json")
        
        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 加密密钥
        self.cipher_suite = None
        self.accounts = []
        self.current_account = None
        
        # 监控相关
        self.monitoring = False
        self.monitor_thread = None
        
        # 自动切换相关
        self.auto_switching = False
        self.auto_switch_thread = None
        self.last_switch_index = 0
        
        # Windsurf切换器
        self.switcher = WindsurfSwitcher()
        
        self.setup_ui()
        self.load_config()
        
    def generate_key(self, password):
        """从密码生成加密密钥"""
        password = password.encode('utf-8')
        salt = b'windsurf_salt_2024'  # 固定salt，生产环境建议随机生成
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return Fernet(key)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="Windsurf账号管理器", font=("微软雅黑", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 密码输入框（首次使用）
        password_frame = ttk.LabelFrame(main_frame, text="安全验证", padding="10")
        password_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        password_frame.columnconfigure(1, weight=1)
        
        ttk.Label(password_frame, text="管理密码:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(password_frame, textvariable=self.password_var, show="*", width=30)
        self.password_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        self.unlock_btn = ttk.Button(password_frame, text="解锁", command=self.unlock_accounts)
        self.unlock_btn.grid(row=0, column=2)
        
        # 主要功能区域
        self.main_notebook = ttk.Notebook(main_frame)
        self.main_notebook.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 账号管理标签页
        self.setup_accounts_tab()
        
        # 切换控制标签页
        self.setup_switch_tab()
        
        # 监控统计标签页
        self.setup_monitor_tab()
        
        # 状态栏
        self.status_var = tk.StringVar(value="请输入管理密码以开始使用")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_label.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 绑定回车键到解锁按钮
        self.password_entry.bind('<Return>', lambda e: self.unlock_accounts())
        
        # 初始状态下禁用标签页
        self.notebook_locked = True
        self._update_notebook_state()
    
    def setup_accounts_tab(self):
        """设置账号管理标签页"""
        accounts_frame = ttk.Frame(self.main_notebook)
        self.main_notebook.add(accounts_frame, text="账号管理")
        
        # 左侧：账号列表
        left_frame = ttk.LabelFrame(accounts_frame, text="账号列表", padding="10")
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        left_frame.rowconfigure(0, weight=1)
        left_frame.columnconfigure(0, weight=1)
        
        # 账号列表
        self.accounts_tree = ttk.Treeview(left_frame, columns=("name", "status", "last_used"), show="tree headings", height=15)
        self.accounts_tree.heading("#0", text="序号")
        self.accounts_tree.heading("name", text="账号名称")
        self.accounts_tree.heading("status", text="状态")
        self.accounts_tree.heading("last_used", text="最后使用")
        
        self.accounts_tree.column("#0", width=50, minwidth=50)
        self.accounts_tree.column("name", width=150, minwidth=100)
        self.accounts_tree.column("status", width=80, minwidth=60)
        self.accounts_tree.column("last_used", width=120, minwidth=100)
        
        scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar.set)
        
        self.accounts_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 右侧：账号操作
        right_frame = ttk.LabelFrame(accounts_frame, text="账号操作", padding="10")
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(1, weight=1)
        
        # 添加账号
        ttk.Label(right_frame, text="账号名称:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.account_name_var = tk.StringVar()
        ttk.Entry(right_frame, textvariable=self.account_name_var, width=30).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Label(right_frame, text="Token:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.token_var = tk.StringVar()
        token_entry = tk.Text(right_frame, height=4, width=40, wrap=tk.WORD)
        token_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 10))
        self.token_text = token_entry
        
        # 按钮区域
        btn_frame = ttk.Frame(right_frame)
        btn_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        btn_frame.columnconfigure((0, 1, 2, 3), weight=1)
        
        ttk.Button(btn_frame, text="添加账号", command=self.add_account).grid(row=0, column=0, padx=(0, 5), sticky=(tk.W, tk.E))
        ttk.Button(btn_frame, text="更新账号", command=self.update_account).grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E))
        ttk.Button(btn_frame, text="删除账号", command=self.delete_account).grid(row=0, column=2, padx=5, sticky=(tk.W, tk.E))
        ttk.Button(btn_frame, text="测试Token", command=self.test_token).grid(row=0, column=3, padx=(5, 0), sticky=(tk.W, tk.E))
        
        # 批量操作
        batch_frame = ttk.LabelFrame(right_frame, text="批量操作", padding="10")
        batch_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(20, 0))
        batch_frame.columnconfigure((0, 1), weight=1)
        
        ttk.Button(batch_frame, text="导入账号", command=self.import_accounts).grid(row=0, column=0, padx=(0, 5), sticky=(tk.W, tk.E))
        ttk.Button(batch_frame, text="导出账号", command=self.export_accounts).grid(row=0, column=1, padx=(5, 0), sticky=(tk.W, tk.E))
        
        # 系统诊断
        diag_frame = ttk.LabelFrame(right_frame, text="系统诊断", padding="10")
        diag_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        diag_frame.columnconfigure(0, weight=1)
        
        ttk.Button(diag_frame, text="诊断Windsurf配置", command=self.diagnose_windsurf).grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 配置网格权重
        accounts_frame.columnconfigure(0, weight=1)
        accounts_frame.columnconfigure(1, weight=1)
        accounts_frame.rowconfigure(0, weight=1)
        
        # 绑定选择事件
        self.accounts_tree.bind("<<TreeviewSelect>>", self.on_account_select)
    
    def setup_switch_tab(self):
        """设置切换控制标签页"""
        switch_frame = ttk.Frame(self.main_notebook)
        self.main_notebook.add(switch_frame, text="切换控制")
        
        # 当前账号信息
        current_frame = ttk.LabelFrame(switch_frame, text="当前活动账号", padding="10")
        current_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        current_frame.columnconfigure(1, weight=1)
        
        ttk.Label(current_frame, text="账号名称:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.current_account_var = tk.StringVar(value="无")
        ttk.Label(current_frame, textvariable=self.current_account_var, font=("微软雅黑", 10, "bold")).grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(current_frame, text="切换时间:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.switch_time_var = tk.StringVar(value="无")
        ttk.Label(current_frame, textvariable=self.switch_time_var).grid(row=1, column=1, sticky=tk.W)
        
        # 快速切换区域
        quick_frame = ttk.LabelFrame(switch_frame, text="快速切换", padding="10")
        quick_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        quick_frame.columnconfigure(0, weight=1)
        quick_frame.rowconfigure(1, weight=1)
        
        ttk.Label(quick_frame, text="选择要切换的账号:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        # 账号选择列表
        self.switch_tree = ttk.Treeview(quick_frame, columns=("name", "status"), show="tree headings", height=8)
        self.switch_tree.heading("#0", text="序号")
        self.switch_tree.heading("name", text="账号名称")
        self.switch_tree.heading("status", text="状态")
        
        self.switch_tree.column("#0", width=50)
        self.switch_tree.column("name", width=150)
        self.switch_tree.column("status", width=80)
        
        switch_scrollbar = ttk.Scrollbar(quick_frame, orient=tk.VERTICAL, command=self.switch_tree.yview)
        self.switch_tree.configure(yscrollcommand=switch_scrollbar.set)
        
        self.switch_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        switch_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        
        # 切换按钮
        ttk.Button(quick_frame, text="立即切换", command=self.switch_account).grid(row=2, column=0, pady=(10, 0))
        
        # 自动切换设置
        auto_frame = ttk.LabelFrame(switch_frame, text="自动切换设置", padding="10")
        auto_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.auto_switch_var = tk.BooleanVar()
        ttk.Checkbutton(auto_frame, text="启用自动切换", variable=self.auto_switch_var, 
                       command=self.toggle_auto_switch).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        
        ttk.Label(auto_frame, text="切换间隔(分钟):").grid(row=1, column=0, sticky=tk.W)
        self.interval_var = tk.StringVar(value="30")
        ttk.Spinbox(auto_frame, from_=5, to=1440, textvariable=self.interval_var, width=10).grid(row=1, column=1, padx=(10, 0))
        
        ttk.Label(auto_frame, text="切换策略:").grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        self.strategy_var = tk.StringVar(value="轮询")
        strategy_combo = ttk.Combobox(auto_frame, textvariable=self.strategy_var, values=["轮询", "随机", "负载均衡"], state="readonly")
        strategy_combo.grid(row=2, column=1, padx=(10, 0), pady=(10, 0))
        
        # 配置网格权重
        switch_frame.columnconfigure(0, weight=1)
        switch_frame.columnconfigure(1, weight=1)
        switch_frame.rowconfigure(1, weight=1)
        
        # 绑定双击切换
        self.switch_tree.bind("<Double-1>", lambda e: self.switch_account())
    
    def setup_monitor_tab(self):
        """设置监控统计标签页"""
        monitor_frame = ttk.Frame(self.main_notebook)
        self.main_notebook.add(monitor_frame, text="监控统计")
        
        # 监控控制
        control_frame = ttk.LabelFrame(monitor_frame, text="监控控制", padding="10")
        control_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.monitor_var = tk.BooleanVar()
        ttk.Checkbutton(control_frame, text="启用实时监控", variable=self.monitor_var, 
                       command=self.toggle_monitoring).grid(row=0, column=0, padx=(0, 20))
        
        self.monitor_status_var = tk.StringVar(value="监控已停止")
        ttk.Label(control_frame, textvariable=self.monitor_status_var, foreground="red").grid(row=0, column=1)
        
        # 使用统计
        stats_frame = ttk.LabelFrame(monitor_frame, text="使用统计", padding="10")
        stats_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.rowconfigure(0, weight=1)
        
        # 统计表格
        self.stats_tree = ttk.Treeview(stats_frame, columns=("name", "usage_count", "last_used", "total_time"), show="tree headings")
        self.stats_tree.heading("#0", text="序号")
        self.stats_tree.heading("name", text="账号名称")
        self.stats_tree.heading("usage_count", text="使用次数")
        self.stats_tree.heading("last_used", text="最后使用")
        self.stats_tree.heading("total_time", text="总使用时长")
        
        self.stats_tree.column("#0", width=50)
        self.stats_tree.column("name", width=120)
        self.stats_tree.column("usage_count", width=80)
        self.stats_tree.column("last_used", width=120)
        self.stats_tree.column("total_time", width=100)
        
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_tree.yview)
        self.stats_tree.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        stats_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 实时信息
        info_frame = ttk.LabelFrame(monitor_frame, text="实时信息", padding="10")
        info_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.info_text = tk.Text(info_frame, height=15, width=40, wrap=tk.WORD, state=tk.DISABLED)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        info_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 清除日志按钮
        ttk.Button(info_frame, text="清除日志", command=self.clear_log).grid(row=1, column=0, pady=(10, 0))
        
        # 配置网格权重
        monitor_frame.columnconfigure(0, weight=1)
        monitor_frame.columnconfigure(1, weight=1)
        monitor_frame.rowconfigure(1, weight=1)
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(0, weight=1)
    
    def _update_notebook_state(self):
        """更新Notebook的可用状态"""
        if self.notebook_locked:
            # 禁用所有标签页
            for i in range(self.main_notebook.index('end')):
                self.main_notebook.tab(i, state='disabled')
        else:
            # 启用所有标签页
            for i in range(self.main_notebook.index('end')):
                self.main_notebook.tab(i, state='normal')
    
    def log_message(self, message):
        """添加日志消息"""
        if hasattr(self, 'info_text'):
            self.info_text.configure(state=tk.NORMAL)
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.info_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.info_text.configure(state=tk.DISABLED)
            self.info_text.see(tk.END)
    
    def clear_log(self):
        """清除日志"""
        self.info_text.configure(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        self.info_text.configure(state=tk.DISABLED)
    
    def unlock_accounts(self):
        """解锁账号数据"""
        password = self.password_var.get()
        if not password:
            messagebox.showerror("错误", "请输入管理密码")
            return
        
        try:
            self.cipher_suite = self.generate_key(password)
            self.load_accounts()
            
            # 启用主功能区域
            self.notebook_locked = False
            self._update_notebook_state()
            self.status_var.set("已解锁，可以使用所有功能")
            
            # 刷新UI
            self.refresh_accounts_display()
            self.refresh_switch_display()
            self.refresh_stats_display()
            
            messagebox.showinfo("成功", "账号数据已解锁")
            
        except Exception as e:
            messagebox.showerror("错误", f"解锁失败：{str(e)}")
    
    def load_accounts(self):
        """加载账号数据"""
        try:
            if os.path.exists(self.accounts_file):
                with open(self.accounts_file, 'rb') as f:
                    encrypted_data = f.read()
                decrypted_data = self.cipher_suite.decrypt(encrypted_data)
                self.accounts = json.loads(decrypted_data.decode('utf-8'))
            else:
                self.accounts = []
        except Exception as e:
            self.accounts = []
            raise Exception(f"加载账号数据失败：{str(e)}")
    
    def save_accounts(self):
        """保存账号数据"""
        try:
            data = json.dumps(self.accounts, ensure_ascii=False, indent=2)
            encrypted_data = self.cipher_suite.encrypt(data.encode('utf-8'))
            with open(self.accounts_file, 'wb') as f:
                f.write(encrypted_data)
        except Exception as e:
            messagebox.showerror("错误", f"保存账号数据失败：{str(e)}")
    
    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.current_account = config.get('current_account')
        except Exception as e:
            self.current_account = None
    
    def save_config(self):
        """保存配置"""
        try:
            config = {
                'current_account': self.current_account
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败：{str(e)}")
    
    def add_account(self):
        """添加账号"""
        name = self.account_name_var.get().strip()
        token = self.token_text.get(1.0, tk.END).strip()
        
        if not name or not token:
            messagebox.showerror("错误", "请填写账号名称和Token")
            return
        
        # 检查重名
        for account in self.accounts:
            if account['name'] == name:
                messagebox.showerror("错误", "账号名称已存在")
                return
        
        # 添加账号
        account = {
            'name': name,
            'token': token,
            'created_time': datetime.now().isoformat(),
            'last_used': None,
            'usage_count': 0,
            'total_time': 0,
            'status': 'available'
        }
        
        self.accounts.append(account)
        self.save_accounts()
        
        # 清空输入框
        self.account_name_var.set("")
        self.token_text.delete(1.0, tk.END)
        
        # 刷新显示
        self.refresh_accounts_display()
        self.refresh_switch_display()
        
        messagebox.showinfo("成功", f"账号 '{name}' 添加成功")
        self.log_message(f"添加账号：{name}")
    
    def update_account(self):
        """更新账号"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showerror("错误", "请先选择要更新的账号")
            return
        
        # 获取选中的账号索引
        item = self.accounts_tree.item(selection[0])
        account_index = int(item['text']) - 1
        
        name = self.account_name_var.get().strip()
        token = self.token_text.get(1.0, tk.END).strip()
        
        if not name or not token:
            messagebox.showerror("错误", "请填写账号名称和Token")
            return
        
        # 检查重名（排除当前账号）
        for i, account in enumerate(self.accounts):
            if i != account_index and account['name'] == name:
                messagebox.showerror("错误", "账号名称已存在")
                return
        
        # 更新账号
        self.accounts[account_index]['name'] = name
        self.accounts[account_index]['token'] = token
        self.save_accounts()
        
        # 刷新显示
        self.refresh_accounts_display()
        self.refresh_switch_display()
        
        messagebox.showinfo("成功", f"账号 '{name}' 更新成功")
        self.log_message(f"更新账号：{name}")
    
    def delete_account(self):
        """删除账号"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showerror("错误", "请先选择要删除的账号")
            return
        
        # 获取选中的账号
        item = self.accounts_tree.item(selection[0])
        account_index = int(item['text']) - 1
        account_name = self.accounts[account_index]['name']
        
        # 确认删除
        if messagebox.askyesno("确认删除", f"确定要删除账号 '{account_name}' 吗？"):
            del self.accounts[account_index]
            self.save_accounts()
            
            # 清空输入框
            self.account_name_var.set("")
            self.token_text.delete(1.0, tk.END)
            
            # 刷新显示
            self.refresh_accounts_display()
            self.refresh_switch_display()
            
            messagebox.showinfo("成功", f"账号 '{account_name}' 删除成功")
            self.log_message(f"删除账号：{account_name}")
    
    def test_token(self):
        """测试Token有效性"""
        token = self.token_text.get(1.0, tk.END).strip()
        if not token:
            messagebox.showerror("错误", "请输入Token")
            return
        
        try:
            self.status_var.set("正在测试Token...")
            self.root.update()
            
            # 使用实际的Token验证
            is_valid, message = self.switcher.validate_token(token)
            
            if is_valid:
                messagebox.showinfo("测试结果", message)
                self.status_var.set("Token验证成功")
            else:
                messagebox.showerror("测试失败", message)
                self.status_var.set("Token验证失败")
            
        except Exception as e:
            messagebox.showerror("测试失败", f"Token验证失败：{str(e)}")
            self.status_var.set("Token测试失败")
    
    def on_account_select(self, event):
        """账号选择事件"""
        selection = self.accounts_tree.selection()
        if selection:
            item = self.accounts_tree.item(selection[0])
            account_index = int(item['text']) - 1
            account = self.accounts[account_index]
            
            # 填充输入框
            self.account_name_var.set(account['name'])
            self.token_text.delete(1.0, tk.END)
            self.token_text.insert(1.0, account['token'])
    
    def refresh_accounts_display(self):
        """刷新账号显示"""
        # 清空树形控件
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)
        
        # 添加账号数据
        for i, account in enumerate(self.accounts):
            last_used = account.get('last_used', '从未使用')
            if last_used and last_used != '从未使用':
                try:
                    last_used_dt = datetime.fromisoformat(last_used)
                    last_used = last_used_dt.strftime("%m-%d %H:%M")
                except:
                    pass
            
            self.accounts_tree.insert('', 'end', text=str(i+1), values=(
                account['name'],
                account.get('status', 'available'),
                last_used
            ))
    
    def refresh_switch_display(self):
        """刷新切换显示"""
        # 清空树形控件
        for item in self.switch_tree.get_children():
            self.switch_tree.delete(item)
        
        # 添加账号数据
        for i, account in enumerate(self.accounts):
            self.switch_tree.insert('', 'end', text=str(i+1), values=(
                account['name'],
                account.get('status', 'available')
            ))
    
    def refresh_stats_display(self):
        """刷新统计显示"""
        # 清空树形控件
        for item in self.stats_tree.get_children():
            self.stats_tree.delete(item)
        
        # 添加统计数据
        for i, account in enumerate(self.accounts):
            last_used = account.get('last_used', '从未使用')
            if last_used and last_used != '从未使用':
                try:
                    last_used_dt = datetime.fromisoformat(last_used)
                    last_used = last_used_dt.strftime("%m-%d %H:%M")
                except:
                    pass
            
            total_time = account.get('total_time', 0)
            total_time_str = f"{total_time//3600}h{(total_time%3600)//60}m" if total_time > 0 else "0m"
            
            self.stats_tree.insert('', 'end', text=str(i+1), values=(
                account['name'],
                account.get('usage_count', 0),
                last_used,
                total_time_str
            ))
    
    def switch_account(self):
        """切换账号"""
        selection = self.switch_tree.selection()
        if not selection:
            messagebox.showerror("错误", "请先选择要切换的账号")
            return
        
        item = self.switch_tree.item(selection[0])
        account_index = int(item['text']) - 1
        account = self.accounts[account_index]
        
        try:
            # 使用实际的切换功能
            self.status_var.set(f"正在切换到账号: {account['name']}...")
            self.root.update()
            
            # 检查Windsurf是否正在运行
            is_running = self.switcher.is_windsurf_running()
            if is_running:
                result = messagebox.askyesno("确认切换", 
                    "Windsurf正在运行，切换账号将重启应用程序。\n确定要继续吗？")
                if not result:
                    self.status_var.set("切换已取消")
                    return
            
            # 执行切换
            success = self.switcher.switch_token(account['token'], account['name'])
            
            if success:
                # 更新账号使用统计
                self.current_account = account['name']
                account['last_used'] = datetime.now().isoformat()
                account['usage_count'] = account.get('usage_count', 0) + 1
                account['status'] = 'active'
                
                # 将其他账号设为非活动状态
                for acc in self.accounts:
                    if acc['name'] != account['name']:
                        acc['status'] = 'available'
                
                self.save_accounts()
                self.save_config()
                
                # 更新UI
                self.current_account_var.set(account['name'])
                self.switch_time_var.set(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                
                self.refresh_accounts_display()
                self.refresh_switch_display()
                self.refresh_stats_display()
                
                self.log_message(f"已切换到账号：{account['name']}")
                self.status_var.set(f"成功切换到账号: {account['name']}")
                
                if is_running:
                    messagebox.showinfo("成功", f"已切换到账号：{account['name']}\n\nWindsurf已重启，请稍后手动启动。")
                else:
                    messagebox.showinfo("成功", f"已切换到账号：{account['name']}")
            else:
                messagebox.showerror("错误", "账号切换失败，请检查日志")
                self.status_var.set("账号切换失败")
            
        except Exception as e:
            messagebox.showerror("错误", f"切换账号失败：{str(e)}")
            self.status_var.set("切换失败")
            self.log_message(f"切换失败: {str(e)}")
    
    def toggle_auto_switch(self):
        """切换自动切换状态"""
        if self.auto_switch_var.get():
            if not self.accounts:
                messagebox.showwarning("警告", "没有可用的账号，无法启用自动切换")
                self.auto_switch_var.set(False)
                return
            
            self.auto_switching = True
            self.log_message("启用自动切换")
            
            # 启动自动切换线程
            if not self.auto_switch_thread or not self.auto_switch_thread.is_alive():
                self.auto_switch_thread = threading.Thread(target=self.auto_switch_process, daemon=True)
                self.auto_switch_thread.start()
        else:
            self.auto_switching = False
            self.log_message("禁用自动切换")
    
    def auto_switch_process(self):
        """自动切换进程"""
        while self.auto_switching:
            try:
                # 获取切换间隔
                interval_minutes = int(self.interval_var.get())
                interval_seconds = interval_minutes * 60
                
                # 等待指定时间
                for _ in range(interval_seconds):
                    if not self.auto_switching:
                        return
                    time.sleep(1)
                
                if self.auto_switching and self.accounts:
                    # 根据策略选择下一个账号
                    next_account = self._get_next_account()
                    if next_account:
                        self._auto_switch_to_account(next_account)
                        
            except Exception as e:
                self.log_message(f"自动切换错误：{str(e)}")
                time.sleep(60)  # 错误后等待一分钟
    
    def _get_next_account(self):
        """根据策略获取下一个账号"""
        if not self.accounts:
            return None
            
        strategy = self.strategy_var.get()
        
        if strategy == "轮询":
            # 轮询策略
            self.last_switch_index = (self.last_switch_index + 1) % len(self.accounts)
            return self.accounts[self.last_switch_index]
            
        elif strategy == "随机":
            # 随机策略
            import random
            return random.choice(self.accounts)
            
        elif strategy == "负载均衡":
            # 负载均衡策略：选择使用次数最少的账号
            min_usage = min(acc.get('usage_count', 0) for acc in self.accounts)
            candidates = [acc for acc in self.accounts if acc.get('usage_count', 0) == min_usage]
            import random
            return random.choice(candidates)
            
        return self.accounts[0]  # 默认返回第一个
    
    def _auto_switch_to_account(self, account):
        """自动切换到指定账号"""
        try:
            # 检查是否已经是当前账号
            if self.current_account == account['name']:
                self.log_message(f"当前已是账号 {account['name']}，跳过切换")
                return
            
            self.log_message(f"自动切换到账号: {account['name']}")
            
            # 使用实际的切换功能
            success = self.switcher.switch_token(account['token'], account['name'])
            
            if success:
                # 更新账号信息
                self.current_account = account['name']
                account['last_used'] = datetime.now().isoformat()
                account['usage_count'] = account.get('usage_count', 0) + 1
                account['status'] = 'active'
                
                # 将其他账号设为非活动状态
                for acc in self.accounts:
                    if acc['name'] != account['name']:
                        acc['status'] = 'available'
                
                self.save_accounts()
                self.save_config()
                
                # 更新UI（在主线程中执行）
                self.root.after(0, self._update_ui_after_auto_switch, account)
                
                self.log_message(f"自动切换成功: {account['name']}")
            else:
                self.log_message(f"自动切换失败: {account['name']}")
                
        except Exception as e:
            self.log_message(f"自动切换异常: {str(e)}")
    
    def _update_ui_after_auto_switch(self, account):
        """自动切换后更新UI"""
        self.current_account_var.set(account['name'])
        self.switch_time_var.set(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        self.refresh_accounts_display()
        self.refresh_switch_display()
        self.refresh_stats_display()
    
    def toggle_monitoring(self):
        """切换监控状态"""
        if self.monitor_var.get():
            self.monitoring = True
            self.monitor_status_var.set("监控运行中")
            self.log_message("启用实时监控")
            
            # 启动监控线程
            if not self.monitor_thread or not self.monitor_thread.is_alive():
                self.monitor_thread = threading.Thread(target=self.monitor_process, daemon=True)
                self.monitor_thread.start()
        else:
            self.monitoring = False
            self.monitor_status_var.set("监控已停止")
            self.log_message("停止实时监控")
    
    def monitor_process(self):
        """监控进程"""
        while self.monitoring:
            try:
                # 检查Windsurf运行状态
                is_running = self.switcher.is_windsurf_running()
                if is_running:
                    self.log_message("监控: Windsurf正在运行")
                else:
                    self.log_message("监控: Windsurf未运行")
                
                # 检查当前Token状态
                try:
                    current_token = self.switcher.get_current_token()
                    if current_token:
                        # 查找当前使用的账号
                        current_account_name = None
                        for account in self.accounts:
                            if account['token'] == current_token:
                                current_account_name = account['name']
                                break
                        
                        if current_account_name:
                            self.log_message(f"监控: 当前活动账号 - {current_account_name}")
                        else:
                            self.log_message("监控: 检测到未知Token")
                    else:
                        self.log_message("监控: 未检测到Token")
                except Exception as e:
                    self.log_message(f"监控: Token检查失败 - {str(e)}")
                
                # 检查系统资源
                try:
                    import psutil
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory = psutil.virtual_memory()
                    self.log_message(f"监控: CPU {cpu_percent}%, 内存 {memory.percent}%")
                except Exception as e:
                    self.log_message(f"监控: 系统监控失败 - {str(e)}")
                
                # 等待下次监控
                time.sleep(30)  # 每30秒监控一次
                
            except Exception as e:
                self.log_message(f"监控错误：{str(e)}")
                time.sleep(10)
    
    def import_accounts(self):
        """导入账号"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择账号文件",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_accounts = json.load(f)
                
                # 验证数据格式
                if not isinstance(imported_accounts, list):
                    messagebox.showerror("错误", "文件格式错误")
                    return
                
                # 添加账号（跳过重复的）
                added_count = 0
                for acc in imported_accounts:
                    if 'name' in acc and 'token' in acc:
                        # 检查是否已存在
                        exists = any(existing['name'] == acc['name'] for existing in self.accounts)
                        if not exists:
                            new_account = {
                                'name': acc['name'],
                                'token': acc['token'],
                                'created_time': datetime.now().isoformat(),
                                'last_used': acc.get('last_used'),
                                'usage_count': acc.get('usage_count', 0),
                                'total_time': acc.get('total_time', 0),
                                'status': 'available'
                            }
                            self.accounts.append(new_account)
                            added_count += 1
                
                self.save_accounts()
                self.refresh_accounts_display()
                self.refresh_switch_display()
                
                messagebox.showinfo("成功", f"成功导入 {added_count} 个账号")
                self.log_message(f"导入账号：{added_count}个")
                
        except Exception as e:
            messagebox.showerror("错误", f"导入失败：{str(e)}")
    
    def export_accounts(self):
        """导出账号"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="保存账号文件",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if file_path:
                export_data = []
                for account in self.accounts:
                    export_data.append({
                        'name': account['name'],
                        'token': account['token'],
                        'last_used': account.get('last_used'),
                        'usage_count': account.get('usage_count', 0),
                        'total_time': account.get('total_time', 0)
                    })
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("成功", f"成功导出 {len(export_data)} 个账号")
                self.log_message(f"导出账号：{len(export_data)}个")
                
        except Exception as e:
            messagebox.showerror("错误", f"导出失败：{str(e)}")
    
    def diagnose_windsurf(self):
        """诊断Windsurf配置"""
        try:
            self.log_message("开始诊断Windsurf配置...")
            
            # 在日志中显示诊断结果
            existing_dirs = self.switcher.diagnose_config()
            
            # 在界面中显示结果
            if existing_dirs:
                result = f"找到 {len(existing_dirs)} 个可能的Windsurf配置目录\n\n"
                for i, dir_path in enumerate(existing_dirs, 1):
                    result += f"{i}. {dir_path}\n"
                
                result += "\n请查看日志获取详细信息。"
                messagebox.showinfo("诊断结果", result)
            else:
                messagebox.showwarning("诊断结果", 
                    "未找到Windsurf配置目录\n\n"
                    "请确保：\n"
                    "1. Windsurf已正确安装\n"
                    "2. 至少运行过一次Windsurf\n"
                    "3. 已登录你的账号")
            
            self.log_message("诊断完成")
            
        except Exception as e:
            error_msg = f"诊断失败：{str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)

if __name__ == "__main__":
    app = WindsurfAccountManager()
    app.root.mainloop()